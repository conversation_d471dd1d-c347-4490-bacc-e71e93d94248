#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
header:
  license:
    spdx-id: Apache-2.0
    copyright-owner: Apache Software Foundation

  license-location-threshold: 360

  paths-ignore:
    - '.gitignore'
    - '.gitattributes'
    - '.gitmodules'
    - 'LICENSE'
    - 'NOTICE'
    - '**/*.json'
    - '**/*.key'
    - '**/*.crt'
    - '**/*.pem'
    - '**/*.pb.go'
    - '.github/'
    - 'conf/mime.types'
    - '**/*.svg'
    # Exclude CI env_file
    - 'ci/pod/**/*.env'
    # eyes has some limitation to handle git pattern
    - '**/*.log'
    # Exclude test toolkit files
    - 't/toolkit'
    - 'go.mod'
    - 'go.sum'
    # Exclude non-Apache licensed files
    - 'apisix/balancer/ewma.lua'
    # Exclude plugin-specific configuration files
    - 't/plugin/authz-casbin'
    - 't/coredns'
    - 't/fuzzing/requirements.txt'
    - 'autodocs/'
    - 'docs/**/*.md'
    - '.ignore_words'
    - '.luacheckrc'
    # Exclude file contains certificate revocation information
    - 't/certs/ocsp/index.txt'

  comment: on-failure
