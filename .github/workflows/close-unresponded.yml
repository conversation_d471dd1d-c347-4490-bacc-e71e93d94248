name: Check Issues

on:
  workflow_dispatch:
  schedule:
  - cron: '0 10 * * *'

permissions:
  contents: read

jobs:
  prune_stale:
    permissions:
      issues: write  # for actions/stale to close stale issues
    name: <PERSON>rune Unresponded
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: <PERSON><PERSON><PERSON>
      uses: actions/stale@v8
      with:
        days-before-issue-stale: 60
        days-before-issue-close: 3
        stale-issue-message: >
          Due to lack of the reporter's response this issue has been labeled with "no response".
          It will be close in 3 days if no further activity occurs. If this issue is still
          relevant, please simply write any comment. Even if closed, you can still revive the
          issue at any time or discuss it <NAME_EMAIL> list.
          Thank you for your contributions.
        close-issue-message: >
          This issue has been closed due to lack of activity. If you think that
          is incorrect, or the issue requires additional review, you can revive the issue at
          any time.
        # Issues with these labels will never be considered stale.
        only-labels: 'wait for update'
        stale-issue-label: 'no response'
        exempt-issue-labels: "don't close"
        ascending: true
