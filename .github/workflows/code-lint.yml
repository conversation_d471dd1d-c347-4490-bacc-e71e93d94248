name: Code Lint

on:
  pull_request:
    branches: [master, 'release/**']
    paths-ignore:
      - 'docs/**'
      - '**/*.md'

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
    - uses: actions/checkout@v4
    - name: Install
      run: |
        . ./ci/common.sh
        export_or_prefix
        export OPENRESTY_VERSION=default

        sudo -E ./ci/linux-install-openresty.sh
        ./utils/linux-install-luarocks.sh
        sudo -E luarocks install luacheck

    - name: Script
      run: |
        . ./ci/common.sh
        export_or_prefix
        make lint

  sc-lint:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Shellcheck code
        run: |
          scversion="latest"
          wget -qO- "https://github.com/koalaman/shellcheck/releases/download/${scversion?}/shellcheck-${scversion?}.linux.x86_64.tar.xz" | tar -xJv
          cp -av "shellcheck-${scversion}/shellcheck" /usr/local/bin/
          shellcheck --version
          git ls-files -- "*.sh" | xargs -t shellcheck
