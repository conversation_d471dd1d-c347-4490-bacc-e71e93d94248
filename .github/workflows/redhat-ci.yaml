name: CI Redhat UBI - Daily

on:
  schedule:
    - cron: "0 0 * * *"
  pull_request:
    branches: [master]
    paths-ignore:
      - 'docs/**'
      - '**/*.md'
concurrency:
  group: ${{ github.workflow }}-${{ github.ref == 'refs/heads/master' && github.run_number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test_apisix:
    name: run ci on redhat ubi
    runs-on: ubuntu-latest
    timeout-minutes: 90
    strategy:
      fail-fast: false
      matrix:
        events_module:
          - lua-resty-worker-events
          - lua-resty-events
        test_dir:
          - t/plugin/[a-k]*
          - t/plugin/[l-z]*
          - t/admin t/cli t/config-center-yaml t/control t/core t/debug t/discovery t/error_page t/misc
          - t/node t/pubsub t/router t/script t/secret t/stream-node t/utils t/xds-library

    steps:
    - name: Check out code
      uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Cache deps
      uses: actions/cache@v4
      env:
        cache-name: cache-deps
      with:
        path: deps
        key: ${{ runner.os }}-${{ env.cache-name }}-ubi8.6-${{ hashFiles('apisix-master-0.rockspec') }}

    - name: Extract branch name
      if: ${{ startsWith(github.ref, 'refs/heads/release/') }}
      id: branch_env
      shell: bash
      run: |
        echo "version=${GITHUB_REF##*/}" >>$GITHUB_OUTPUT

    - name: Extract test type
      shell: bash
      id: test_env
      run: |
        test_dir="${{ matrix.test_dir }}"
        if [[ $test_dir =~ 't/plugin' ]]; then
          echo "type=plugin" >>$GITHUB_OUTPUT
        fi
        if [[ $test_dir =~ 't/admin ' ]]; then
          echo "type=first" >>$GITHUB_OUTPUT
        fi
        if [[ $test_dir =~ ' t/xds-library' ]]; then
          echo "type=last" >>$GITHUB_OUTPUT
        fi

    - name: Free disk space
      run: |
        bash ./ci/free_disk_space.sh

    - name: Linux launch common services
      run: |
        make ci-env-up project_compose_ci=ci/pod/docker-compose.common.yml
        sudo ./ci/init-common-test-service.sh

    - name: Build rpm package
      if: ${{ startsWith(github.ref, 'refs/heads/release/') }}
      run: |
        export VERSION=${{ steps.branch_env.outputs.version }}
        sudo gem install --no-document fpm
        git clone --depth 1 https://github.com/api7/apisix-build-tools.git

        # move codes under build tool
        mkdir ./apisix-build-tools/apisix
        for dir in `ls|grep -v "^apisix-build-tools$"`;do cp -r $dir ./apisix-build-tools/apisix/;done

        cd apisix-build-tools
        make package type=rpm app=apisix version=${VERSION} checkout=release/${VERSION} image_base=ubi image_tag=8.6 local_code_path=./apisix
        cd ..
        rm -rf $(ls -1 -I apisix-build-tools -I t -I utils -I ci --ignore=Makefile -I "*.rockspec")

    - name: Start Dubbo Backend
      run: |
        cur_dir=$(pwd)
        sudo apt update
        sudo apt install -y maven
        cd t/lib/dubbo-backend
        mvn package
        cd dubbo-backend-provider/target
        java \
            -Djava.net.preferIPv4Stack=true \
            -jar dubbo-demo-provider.one-jar.jar > /tmp/java.log &
        cd $cur_dir/t/lib/dubbo-serialization-backend
        mvn package
        cd dubbo-serialization-backend-provider/target
        java \
            -Djava.net.preferIPv4Stack=true \
            -jar dubbo-demo-provider.one-jar.jar > /tmp/java2.log &

    - name: Build xDS library
      if: steps.test_env.outputs.type == 'last'
      run: |
        cd t/xds-library
        go build -o libxds.so -buildmode=c-shared main.go export.go

    - name: Run redhat docker and mapping apisix into container
      env:
        TEST_FILE_SUB_DIR: ${{ matrix.test_dir }}
        TEST_EVENTS_MODULE: ${{ matrix.events_module }}
      run: |
        docker run -itd -v ${{ github.workspace }}:/apisix --env TEST_FILE_SUB_DIR="$TEST_FILE_SUB_DIR" --env TEST_EVENTS_MODULE="$TEST_EVENTS_MODULE" --name ubiInstance --net="host" --dns ******* --dns-search apache.org registry.access.redhat.com/ubi8/ubi:8.6 /bin/bash

    - name: Cache images
      id: cache-images
      uses: actions/cache@v4
      env:
        cache-name: cache-apisix-docker-images
      with:
        path: docker-images-backup
        key: ${{ runner.os }}-${{ env.cache-name }}-${{ steps.test_env.outputs.type }}-${{ hashFiles(format('./ci/pod/docker-compose.{0}.yml', steps.test_env.outputs.type )) }}

    - if: ${{ steps.cache-images.outputs.cache-hit == 'true' }}
      name: Load saved docker images
      run: |
        if [[ -f docker-images-backup/apisix-images.tar ]]; then
          [[ ${{ steps.test_env.outputs.type }} != first ]] && sudo ./ci/init-${{ steps.test_env.outputs.type }}-test-service.sh before
          docker load --input docker-images-backup/apisix-images.tar
          rm docker-images-backup/apisix-images.tar
          make ci-env-up project_compose_ci=ci/pod/docker-compose.${{ steps.test_env.outputs.type }}.yml
          echo "loaded docker images"
          if [[ ${{ steps.test_env.outputs.type }} != first ]]; then
            sudo ./ci/init-${{ steps.test_env.outputs.type }}-test-service.sh after
          fi
        fi
    - if: ${{ steps.cache-images.outputs.cache-hit != 'true' }}
      name: Linux launch services
      run: |
        [[ ${{ steps.test_env.outputs.type }} != first ]] && sudo ./ci/init-${{ steps.test_env.outputs.type }}-test-service.sh before
        [[ ${{ steps.test_env.outputs.type }} == plugin ]] && ./ci/pod/openfunction/build-function-image.sh
        make ci-env-up project_compose_ci=ci/pod/docker-compose.${{ steps.test_env.outputs.type }}.yml
        [[ ${{ steps.test_env.outputs.type }} != first ]] && sudo ./ci/init-${{ steps.test_env.outputs.type }}-test-service.sh after
        echo "Linux launch services, done."

    - name: Install dependencies
      run: |
        docker exec ubiInstance bash -c "cd apisix && chmod +x ./ci/redhat-ci.sh && ./ci/redhat-ci.sh install_dependencies"

    - name: Install rpm package
      if: ${{ startsWith(github.ref, 'refs/heads/release/') }}
      run: |
        docker exec ubiInstance bash -c "cd apisix && rpm -iv --prefix=/apisix ./apisix-build-tools/output/apisix-${{ steps.branch_env.outputs.version }}-0.ubi8.6.x86_64.rpm"
        # Dependencies are attached with rpm, so revert `make deps`
        docker exec ubiInstance bash -c "cd apisix && rm -rf deps"
        docker exec ubiInstance bash -c "cd apisix && mv usr/bin . && mv usr/local/apisix/* ."

    - name: Run test cases
      run: |
        docker exec ubiInstance bash -c "cd apisix && chmod +x ./ci/redhat-ci.sh && ./ci/redhat-ci.sh run_case"

    - if: ${{ steps.cache-images.outputs.cache-hit != 'true' }}
      name: Save docker images
      run: |
        echo "start backing up, $(date)"
        bash ./ci/backup-docker-images.sh ${{ steps.test_env.outputs.type }}
        echo "backup done, $(date)"
