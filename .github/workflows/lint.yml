name: ❄️ Lint

on: [push, pull_request]

permissions:
  contents: read

jobs:
  misc:
    name: misc checker
    runs-on: ubuntu-latest
    steps:
      - name: Check out code.
        uses: actions/checkout@v4
      - name: spell check
        run: |
          pip install codespell==2.1.0
          # codespell considers some repo name in go.sum are misspelled
          git grep --cached -l '' | grep -v go.sum |xargs codespell --ignore-words=.ignore_words
      - name: Merge conflict
        run: |
          bash ./utils/check-merge-conflict.sh
      - name: Plugin Code
        run: |
          bash ./utils/check-plugins-code.sh

  ci-eclint:
    runs-on: ubuntu-latest
    timeout-minutes: 5

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Nodejs env
        uses: actions/setup-node@v4.0.2
        with:
          node-version: '12'

      - name: Install eclint
        run: |
          sudo npm install -g eclint

      - name: Run eclint
        run: |
          eclint check
