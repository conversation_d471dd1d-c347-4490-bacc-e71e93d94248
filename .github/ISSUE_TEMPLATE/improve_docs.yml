name: "Documentation Issue"
description: Issues related to documentation.
title: "docs: "
labels: [doc]
body:
  - type: markdown
    attributes:
      value: |
        _The more information you share, the faster we can help you._

        Prior to opening the issue, please make sure that you:

        - Use English to communicate.
        - Search the [open issues](https://github.com/apache/apisix/issues) and [discussion forum](https://github.com/apache/apisix/discussions) to avoid duplicating the issue.

  - type: textarea
    id: current-state
    attributes:
      label: Current State
      description: Describe the current state of the documentation.
      placeholder: |
        The documentation for the API in this page (url) is missing ...
    validations:
      required: true
  - type: textarea
    id: desired-state
    attributes:
      label: Desired State
      description: Describe the desired state the documentation should be in.
      placeholder: |
        There should be line mentioning how the API behaves when ...
    validations:
      required: true
