#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

github:
    description: The Cloud-Native API Gateway and AI Gateway
    homepage: https://apisix.apache.org/blog/
    labels:
      - api-gateway
      - ai-gateway
      - ai
      - cloud-native
      - nginx
      - luajit
      - apigateway
      - microservices
      - api
      - apis
      - loadbalancing
      - reverse-proxy
      - api-management
      - apisix
      - serverless
      - iot
      - devops
      - kubernetes
      - docker
      - kubernetes-ingress
      - kubernetes-ingress-controller

    enabled_merge_buttons:
      squash:  true
      # **WARNING**: rebase should only be used
      # when backport multiple commits to the `release/xx` branch
      rebase:  true
      merge:   false

    protected_branches:
      master:
        required_pull_request_reviews:
          dismiss_stale_reviews: true
          require_code_owner_reviews: true
          required_approving_review_count: 3
      release/3.11:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 3
      release/3.10:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 3
      release/3.9:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.8:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.7:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.6:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.5:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.4:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.3:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.2:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.1:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/3.0:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.99:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.15:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.14:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.13:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.12:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.11:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.10:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.9:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.8:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.7:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.6:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.5:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2
      release/2.4:
        required_pull_request_reviews:
          require_code_owner_reviews: true
          required_approving_review_count: 2

    notifications:
      commits:      <EMAIL>
      issues:       <EMAIL>
      pullrequests: <EMAIL>
