---
title: Powered by Apache APISIX
---

<!--
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
-->

This page documents an alphabetical list of institutions that are using APISIX for research and production,
or providing commercial products including APISIX.

Users are encouraged to add themselves to this page, [issue](https://github.com/apache/apisix/issues/487) and PR are welcomed.

1. <a href="http://www.aimiaobi.com/" rel="nofollow">aimiaobi 妙笔 AI</a>
1. <a href="http://www.augurit.com/" rel="nofollow">AUGUR 奥格科技股份有限公司</a>
1. <a href="https://cloud.aispeech.com/" rel="nofollow">AISPEECH 思必驰信息科技股份有限公司</a>
1. <a href="http://www.cunw.com.cn/" rel="nofollow">cunw 湖南新云网</a>
1. <a href="https://www.chaolian360.com/" rel="nofollow">Chaolian 超链云商</a>
1. <a href="https://www.ccbft.com/" rel="nofollow">CCB Fintech 建信金科</a>
1. <a href="https://www.ctrl.cn" rel="nofollow">CTRL 开创云</a>
1. <a href="http://51tiangou.com/" rel="nofollow">51tiangou 大商天狗</a>
1. <a href="https://www.daocloud.io/" rel="nofollow">DaoCloud</a>
1. <a href="https://www.dasouche.com/" rel="nofollow">dasouche 大搜车</a>
1. <a href="https://www.dataoke.com/" rel="nofollow">dataoke 大淘客</a>
1. <a href="https://www.didachuxing.com/" rel="nofollow">嘀嗒出行</a>
1. <a href="http://dusto.cn/" rel="nofollow">dusto.cn 浙江大东鞋业有限公司</a>
1. <a href="http://dian.so/" rel="nofollow">Dian 小电科技</a>
1. <a href="https://www.efactory-project.eu/" rel="nofollow">eFactory</a>
1. <a href="https://www.ehomepay.com.cn/" rel="nofollow">ehomepay 理房通</a>
1. <a href="https://ezone.work/" rel="nofollow">eZone 简单一点科技</a>
1. <a href="https://fansup.mobi/" rel="nofollow">fansup</a>
1. <a href="https://game.qq.com/" rel="nofollow">Tencent Game 腾讯游戏</a>
1. <a href="https://www.haier.com/haier-ecosystem/haier/" rel="nofollow">haieruplus 海尔优家</a>
1. <a href="http://www.hellowin.cn/" rel="nofollow">hellowin 好洛维</a>
1. <a href="https://www.hellotalk.com/" rel="nofollow">HelloTalk, Inc.</a>
1. <a href="" rel="nofollow">航天网信</a>
1. <a href="http://huawei.com/" rel="nofollow">Huawei 华为</a>
1. <a href="https://www.huya.com/" rel="nofollow">虎牙</a>
1. <a href="http://www.hys.cn/" rel="nofollow">好医生集团</a>
1. <a href="https://www.ihomefnt.com/" rel="nofollow">ihomefnt 艾佳生活</a>
1. <a href="https://www.intsig.com/" rel="nofollow">intsig 上海合合信息科技股份有限公司</a>
1. <a href="https://www.jiandanxinli.com/" rel="nofollow">jiandanxinli 简单心理</a>
1. <a href="https://jr.ly.com/" rel="nofollow">jr.ly 同程金服</a>
1. <a href="https://www.kaishustory.com/" rel="nofollow">凯叔讲故事</a>
1. <a href="https://www.ke.com/" rel="nofollow">ke.com 贝壳找房</a>
1. <a href="https://www.meizu.com/" rel="nofollow">Meizu 魅族</a>
1. <a href="https://www.mingyuanyun.com/" rel="nofollow">明源云客</a>
1. <a href="https://www.meicai.cn/" rel="nofollow">美菜网</a>
1. <a href="http://www.163.com" rel="nofollow">Netease 网易</a>
1. <a href="https://www.jpl.nasa.gov" rel="nofollow">NASA JPL 美国国家航空航天局 喷气推进实验室</a>
1. <a href="https://www.purcotton.com/" rel="nofollow">Purcotton 深圳全棉时代科技有限公司</a>
1. <a href="https://www.360.cn/" rel="nofollow">360 奇虎</a>
1. <a href="http://www.sinog2c.com/" rel="nofollow">sinog2c 湖南国科云通</a>
1. <a href="https://www.sinovatech.com" rel="nofollow">sinovatech 炎黄新星</a>
1. <a href="http://taikang.com/" rel="nofollow">Taikanglife 泰康云</a>
1. <a href="http://www.tangdou.com/" rel="nofollow">tangdou 糖豆网</a>
1. <a href="https://cloud.tencent.com/" rel="nofollow">Tencent Cloud 腾讯云</a>
1. <a href="https://www.travelsky.com.cn/" rel="nofollow"> Travelsky 中国航信</a>
1. <a href="https://vbill.cn/" rel="nofollow">vbill 随行付</a>
1. <a href="https://www.vivo.com/hk/zh/" rel="nofollow">VIVO</a>
1. <a href="https://www.teamones.cn/" rel="nofollow">万思</a>
1. <a href="https://www.willclass.com/" rel="nofollow">willclass 会课</a>
1. <a href="https://www.wps.cn/" rel="nofollow">金山办公</a>
1. <a href="https://www.xin.com/" rel="nofollow">Xin 优信二手车</a>
1. <a href="https://xueqiu.com/" rel="nofollow">雪球</a>
1. <a href="https://open.youtu.qq.com/" rel="nofollow">Youtu 腾讯优图</a>
1. <a href="http://www.ymm56.com/" rel="nofollow">YMM 满帮集团</a>
1. <a href="https://hy.10086.cn/" rel="nofollow">中移杭研</a>
1. <a href="https://www.zihao.biz/" rel="nofollow">紫豪网络</a>
1. <a href="https://www.zuzuche.com/" rel="nofollow">zuzuche 租租车</a>
1. <a href="https://www.zybang.com/" rel="nofollow">zybang 作业帮</a>
1. <a href="" rel="nofollow">中食安泓（广东）健康产业有限公司</a>
1. <a href="https://appadvice.com/app/e5-8c-bb-e6-82-a3-e5-ae-a2-e6-9c-8d/**********" rel="nofollow">上海泽怡信息科技</a>
1. <a href="https://www.xinpianchang.com" rel="nofollow">北京新片场传媒股份有限公司</a>
1. <a href="https://www.niimbot.com" rel="nofollow">武汉精臣智慧标识科技有限公司</a>
1. <a href="https://www.aiit.org.cn/" rel="nofollow">北京大学信息技术高等研究院</a>
1. <a href="https://www.hihonor.com/cn/" rel="nofollow">HONOR 荣耀</a>
1. <a href="https://www.maiscrm.com/" rel="nofollow">群之脉信息科技</a>
1. <a href="https://www.dafangya.com/" rel="nofollow">大房鸭</a>
1. <a href="http://www.utyun.com/" rel="nofollow">优特云</a>
1. <a href="https://www.unipus.cn/" rel="nofollow">外研在线</a>
1. <a href="https://paramland.com/#/" rel="nofollow">数地科技</a>
1. <a href="https://www.vhall.com/" rel="nofollow">微吼</a>
1. <a href="https://www.xiaopeng.com/" rel="nofollow">小鹏汽车</a>
1. <a href="" rel="nofollow">Ideacreep</a>

<img src="https://user-images.githubusercontent.com/40708551/109484046-f7c4e280-7aa5-11eb-9d71-aab90830773a.png" width="725" height="1700" />

## User Cases

## NASA JPL

Using Apache APISIX as an API gateway to deal with north-south and east-west traffic between microservices.

## ke.com

Using Apache APISIX as traffic entry gateway

## meizu

Using Apache APISIX as api gateway (limit, grpc transcode, abtest, dynamic configures ...)

## zuzuche.com

Using Apache APISIX as a gateway, it uses the functions of current limiting, speed limiting, black-and-white list and so on. In the later stage, it also wants to add gRPC protocol, Serverless, custom plug-in, and other functions to meet business needs.

## souche.com

Using Apache APISIX as a Web ACL gateway to deal with backend OA systems traffic.

## HelloTalk, Inc.

Using Apache APISIX as an API gateway to manage all API and SSL certificates in test\dev\CMS environment.
