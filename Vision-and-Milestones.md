<!--
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
-->

### Vision

Apache APISIX is an open source API gateway designed to help developers connect any APIs securely and efficiently in any environment.

Managing thousands or tens of thousands of APIs and microservices in a multi-cloud and hybrid cloud environment is not an easy task.
There will be many challenges as authentication, observability, security, etc.

Apache APISIX, a community-driven project, hopes to help everyone better manage and use APIs through the power of developers.
Every developer's contribution will used by thousands of companies and served by billions of users.

### Milestones

Apache APISIX has relatively complete features for north-south traffic,
and will be iterated around the following directions in the next 6 months (if you have any ideas, feel free to create issue to discuss):

- More complete support for Gateway API on APISIX ingress controller
- Add support for service mesh
- User-friendly documentation
- More plugins for public cloud and SaaS services
- Java/Go plugins and Wasm production-ready
- Add dynamic debugging tools for Apache APISIX
