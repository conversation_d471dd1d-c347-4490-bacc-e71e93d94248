#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

root = true

[*]
charset = utf-8
end_of_line = lf
indent_style = space
insert_final_newline = true
trim_trailing_whitespace = true

[.gitmodules]
indent_style = tab

[Makefile]
indent_style = tab

[*.{yml,yaml}]
indent_size = 2

[*.go]
indent_style = tab
## ignore ASF license
block_comment_start = /*
block_comment = *
block_comment_end = */

[**go.mod]
indent_style = tab

[t/coredns/db.test.local]
indent_style = unset

[*.pb]
indent_style = unset
insert_final_newline = unset
trim_trailing_whitespace = unset
end_of_line = unset
